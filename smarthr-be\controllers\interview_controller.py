# Standard library imports
import json
import traceback
from contextlib import contextmanager
from typing import List, Optional
import logging

# Third-party imports
from datetime import datetime
import psycopg2
from psycopg2.extras import Json
from fastapi import HTTPException
from langchain_core.messages import HumanMessage
from typing import Union

# Internal imports
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    QA_model,
    EvaluationResult,
    EvaluateInterviewNoQA,
    Interview,
    InterviewHr,
    InterviewTec,
    TranscriptQuestions,
    TranscriptQuestion,
)
from typing import List, Optional
from models.models import SingleQuestions
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def _validate_evaluation_result(result: EvaluationResult, expected_questions_count: int, evaluation_type: str) -> List[str]:
    """
    Validate evaluation results to catch potential misclassifications.

    Args:
        result: The evaluation result to validate
        expected_questions_count: Expected number of questions
        evaluation_type: Type of evaluation for logging ("transcript-based" or "predefined-questions")

    Returns:
        List of validation issues found (empty if no issues)
    """
    issues = []

    # Check if per_question array has correct length
    if len(result.per_question) != expected_questions_count:
        issues.append(f"Expected {expected_questions_count} question evaluations, got {len(result.per_question)}")

    # Count answered vs unanswered questions
    answered_count = 0
    unanswered_count = 0
    valid_responses = 0  # Responses that should count toward percentage
    invalid_responses = 0  # Wrong answers that don't count toward percentage
    junior_responses = 0

    for q_eval in result.per_question:
        # Check if this appears to be an answered question based on explanation
        explanation_lower = q_eval.explanation.lower()

        # Look for indicators of valid responses (including honest admissions of lack of knowledge)
        valid_response_indicators = [
            "basic understanding", "limited experience", "learning",
            "honest admission", "limited knowledge", "lack of knowledge"
        ]

        # Look for indicators of invalid/wrong responses
        invalid_response_indicators = [
            "incorrect", "wrong", "confused", "misunderstood", "invalid",
            "completely wrong", "fundamental misunderstanding", "nonsensical"
        ]

        # Look for indicators of truly unanswered questions (complete silence/no response)
        unanswered_indicators = [
            "no response found", "not answered", "no answer provided",
            "question not addressed", "skipped", "missing response",
            "remained silent", "no verbal response", "candidate did not respond"
        ]

        has_valid_indicator = any(indicator in explanation_lower for indicator in valid_response_indicators)
        has_invalid_indicator = any(indicator in explanation_lower for indicator in invalid_response_indicators)
        has_unanswered_indicator = any(indicator in explanation_lower for indicator in unanswered_indicators)

        # Determine if question was answered based on explanation content
        is_answered = not has_unanswered_indicator

        if is_answered:
            answered_count += 1
            if q_eval.detected_seniority == 'junior':
                junior_responses += 1
                if has_valid_indicator or not has_invalid_indicator:
                    # Count as valid unless explicitly marked as invalid
                    valid_responses += 1
                else:
                    invalid_responses += 1
            else:
                # Mid/senior responses are always valid
                valid_responses += 1
        else:
            unanswered_count += 1

    # Calculate expected percentage based on VALID responses only
    expected_percentage = (valid_responses / expected_questions_count) * 100 if expected_questions_count > 0 else 0
    actual_percentage = result.percentage_of_match

    # Allow for larger tolerance since invalid responses affect percentage calculation
    if abs(expected_percentage - actual_percentage) > 10:
        issues.append(f"Percentage mismatch: calculated {expected_percentage:.1f}% (valid responses) but got {actual_percentage:.1f}%")

    # Check for suspicious patterns
    if junior_responses > 0 and actual_percentage < (junior_responses / expected_questions_count * 100):
        issues.append(f"Possible misclassification: {junior_responses} junior responses but percentage suggests some were not counted")

    # Log detailed breakdown for debugging
    logger.info(f"{evaluation_type.upper()} VALIDATION: answered={answered_count}, unanswered={unanswered_count}, valid_responses={valid_responses}, invalid_responses={invalid_responses}, junior_responses={junior_responses}")
    logger.info(f"{evaluation_type.upper()} VALIDATION: expected_percentage={expected_percentage:.1f}% (based on valid responses), actual_percentage={actual_percentage:.1f}%")

    return issues


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Helper function to build text for LLM prompts
# This function constructs a text prompt based on the provided items.
def get_topics(include: str) -> str:
    """
    This function constructs a text prompt based on the provided items.

    Args:
        include (str): A comma-separated string of items to include in the prompt.

    Returns:
        str: A formatted string based on the provided items.
    """
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


# Core business logic for processing interviews
def process_interview(request: InterviewProcessingRequest):
    """
    This function processes an interview transcript based on the specified request type.

    Args:
        request (InterviewProcessingRequest): The request object containing questions, transcript, and process type.

    Returns:
        The processed result based on the request type.
    """
    if request.process_type == ProcessType.EXTRACT:
        schema = ExtractedAnswers
        task_prompt = (
            "Extract the candidate's actual responses from the interview transcript for each question. "
            "**CRITICAL: In this transcript format, the text following 'Expected Response:' contains the candidate's actual answers.**"
            "Look for response patterns including: "
            "- Text following 'Expected Response:' labels (this IS the candidate's actual response) "
            "- Responses that start with 'CANDIDATE:' "
            "- Any other clearly identifiable candidate answers "
            "Return ONLY what the candidate actually said, in the same order as the questions appear. "
            "IMPORTANT: "
            "**- 'Expected Response:' sections contain the candidate's ACTUAL responses - extract these fully **"
            "- If you find candidate responses in the transcript, extract them exactly as provided "
            "- Only return 'Invalid transcript' if the transcript is truly empty or contains no responses "
            "- Do NOT return 'Invalid transcript' if there are responses following 'Expected Response:' labels "
            "- Each question should have a corresponding response extracted from the 'Expected Response:' section that follows it"
        )

        # Debug logging for answer extraction
        logger.info(f"EXTRACT DEBUG: transcript length = {len(request.transcript) if request.transcript else 0}")
        logger.info(f"EXTRACT DEBUG: transcript preview = {request.transcript[:200] if request.transcript else 'None'}...")
        logger.info(f"EXTRACT DEBUG: number of questions = {len(request.questions)}")
    else:
        schema = ParaphrasedAnswers
        task_prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_msg = HumanMessage(
        content="Questions:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + "\n\nTranscript:\n"
        + request.transcript
    )

    schema_text = get_related_class_definitions(schema)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=schema,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed")

    # Debug logging for AI response
    if request.process_type == ProcessType.EXTRACT:
        logger.info(f"EXTRACT DEBUG: AI result type = {type(result)}")
        logger.info(f"EXTRACT DEBUG: AI result = {str(result)[:500]}...")

    return result


def extract_questions_from_transcript(transcript: str) -> TranscriptQuestions:
    """
    Extract questions from an interview transcript using LLM analysis.

    Args:
        transcript (str): The interview transcript to analyze.

    Returns:
        TranscriptQuestions: The extracted questions with their categories.
    """
    task_prompt = """
        Analyze the interview transcript and extract all questions that were asked during the interview.

        INSTRUCTIONS:
        1. Identify each distinct question asked by the interviewer
        2. Number them sequentially starting from 1
        3. Extract the exact question text as it appears in the transcript
        4. Identify the category/topic for each question if mentioned (e.g., "Technical Skills", "Soft Skills", "Methodologies", "Language - Tools")
        5. Only include actual questions that expect a response from the candidate
        6. Do not include rhetorical questions, greetings, or closing statements

        TRANSCRIPT PATTERNS TO LOOK FOR:
        - Questions ending with "?"
        - Numbered questions (e.g., "1. Can you explain...")
        - Questions starting with interrogative words (What, How, Can, Describe, etc.)
        - Questions preceded by category headers like "TECHNICAL SKILLS"

        Return a JSON object with the extracted questions following the provided schema.
    """

    user_msg = HumanMessage(content=f"Transcript:\n{transcript}")

    schema_text = get_related_class_definitions(TranscriptQuestions)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=TranscriptQuestions,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        # Return empty questions if extraction fails
        return TranscriptQuestions(questions=[])

    return result


# Helper function to run and persist interview processing
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    """
    This function runs the interview processing and persists the results in the database.

    Args:
        interview_id (str): The ID of the interview to process.
        process_type (ProcessType): The type of processing to perform.

    Returns:
        The processed result based on the request type.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        # raise HTTPException(status_code=404, detail="Interview or questionnaire not found")
        return None

    questionnaire, transcript = row
    questions = [q["question"] for q in questionnaire["questions"]]

    req = InterviewProcessingRequest(
        questions=questions, transcript=transcript, process_type=process_type
    )
    result = process_interview(req)

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


def evaluate_interview_with_no_qa(interview_id: str) -> EvaluateInterviewNoQA:
    """
    Evaluate the interview transcript, candidate info and position info.
    Args:
        interview_id (str): The ID of the interview to evaluate.
    Returns:
        EvaluateInterviewNoQA: The evaluation result without question-answer pairs.
    """
    # We should compare transcript, candidate info and position info
    # interviews has position_id, candidate_id, id, transcript_hr
    # For position info we need to retrieve it from positions_smarthr
    # For candidate info we need to retrieve it from candidates_smarthr
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT p.position_info, c.candidate_info, i.transcript_hr
                FROM interviews i
                JOIN positions_smarthr p ON i.position_id = p.id
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Interview not found (evaluate_interview_with_no_qa)")
        position_info, candidate_info, transcript_hr = row

        # """ SCHEMA
        # class EvaluateInterviewNoQA(BaseModel):
        #     overall_seniority: Seniority
        #     percentage_of_match: float
        #     explanation: str
        # """
        task_prompt_no_questions = """
            Evaluate the interview transcript, candidate info and position info.
            Return JSON that matches the provided schema.
            IMPORTANT RULES:
            • Provide an overall_seniority (senior|mid|junior) based on the transcript and candidate info.
            • Look for senior-level indicators: strategic thinking, architectural decisions, best practices, leadership experience, optimization concerns, or deep technical insights
            • Don't default to 'mid' - if responses show clear senior-level competency, classify as senior
            • Senior level requires evidence of advanced thinking, not perfection in all areas
        """
        schema_text = get_related_class_definitions(EvaluateInterviewNoQA)
        result = inference_with_fallback(
            task_prompt=task_prompt_no_questions,
            model_schema=EvaluateInterviewNoQA,
            user_messages=[HumanMessage(content=json.dumps({'position_info': position_info, 'candidate_info': candidate_info, 'transcript': transcript_hr}, ensure_ascii=False))],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            raise RuntimeError("LLM evaluation failed")

        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s;
                """,
                (Json(result.model_dump()), interview_id)
            )
        return result

    except Exception as e:
        # Log the error and raise an HTTPException
        print(f"Error occurred while evaluating interview without QA: {str(e)}")
        logger.error(f"Error occurred while evaluating interview without QA: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Interview not found (except: evaluate_interview_with_no_qa): {str(e)}")


def evaluate_interview_transcript_based(interview_id: str) -> EvaluationResult:
    """
    Evaluate the interview based on questions actually present in the transcript.

    Args:
        interview_id (str): The ID of the interview to evaluate.

    Returns:
        EvaluationResult: The evaluation result based on transcript questions.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT i.transcript_tec, i.feedback_tec, i.position_id
            FROM interviews i
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    if not row or not row[0]:
        raise HTTPException(status_code=404, detail="Interview transcript not found")

    transcript_tec, feedback_tec, position_id = row

    if not position_id:
        raise HTTPException(status_code=400, detail="Position ID not found for interview")

    # Extract questions from transcript
    transcript_questions = extract_questions_from_transcript(transcript_tec)

    # Debug logging
    logger.info(f"TRANSCRIPT EVAL DEBUG: Extracted {len(transcript_questions.questions)} questions from transcript")
    for i, q in enumerate(transcript_questions.questions, 1):
        logger.info(f"TRANSCRIPT EVAL DEBUG: Question {i}: {q.question_text[:100]}...")

    if not transcript_questions.questions:
        raise HTTPException(status_code=400, detail="No questions found in transcript")

    # Retrieve expected answers from generated questions
    expected_questions = None
    try:
        expected_questions = fetch_questions_by_position_id(str(position_id))
        logger.info(f"TRANSCRIPT EVAL DEBUG: Retrieved {len(expected_questions.data.get('questions', []))} expected questions for position {position_id}")
    except HTTPException as e:
        if e.status_code == 404:
            logger.warning(f"TRANSCRIPT EVAL DEBUG: No generated questions found for position {position_id}, proceeding without expected answers")
        else:
            logger.error(f"TRANSCRIPT EVAL DEBUG: Error retrieving questions for position {position_id}: {e.detail}")
    except Exception as e:
        logger.error(f"TRANSCRIPT EVAL DEBUG: Unexpected error retrieving questions for position {position_id}: {str(e)}")

    # Match transcript questions with expected questions
    matched_questions = []
    if expected_questions and expected_questions.data and 'questions' in expected_questions.data:
        expected_q_list = expected_questions.data['questions']

        for transcript_q in transcript_questions.questions:
            # Try to find matching expected question by question number first
            matched_expected = None
            for expected_q in expected_q_list:
                if expected_q.get('question_number') == transcript_q.question_number:
                    matched_expected = expected_q
                    break

            # If no match by number, try basic text similarity (first 50 chars)
            if not matched_expected:
                transcript_text_start = transcript_q.question_text[:50].lower().strip()
                for expected_q in expected_q_list:
                    expected_text_start = expected_q.get('question', '')[:50].lower().strip()
                    if transcript_text_start and expected_text_start and transcript_text_start in expected_text_start:
                        matched_expected = expected_q
                        break

            # Add to matched questions with expected answers if found
            question_with_expected = {
                'transcript_question': transcript_q.model_dump(),
                'expected_answers': matched_expected if matched_expected else None
            }
            matched_questions.append(question_with_expected)

            if matched_expected:
                logger.info(f"TRANSCRIPT EVAL DEBUG: Matched transcript question {transcript_q.question_number} with expected question {matched_expected.get('question_number', 'unknown')}")
            else:
                logger.warning(f"TRANSCRIPT EVAL DEBUG: No expected answer found for transcript question {transcript_q.question_number}")

    # Create evaluation prompt for transcript-based assessment
    task_prompt = f"""
        You are evaluating a candidate's responses in a technical job interview based on the actual questions asked in the transcript.

        EVALUATION APPROACH:
        - You have been provided with {len(transcript_questions.questions)} questions that were identified in the transcript
        - Analyze ONLY these questions and their corresponding candidate responses
        - Calculate percentage_of_match based on questions actually answered vs questions actually asked

        RESPONSE CLASSIFICATION RULES (CRITICAL):
        For PERCENTAGE CALCULATION, distinguish between valid and invalid responses:

        ✓ COUNTS TOWARD PERCENTAGE (valid responses):
        - "I don't know", "I'm not sure", "No experience with that", "Never used it" → rate as 'junior'
        - Brief but relevant responses like "Yes", "No", "Maybe" → rate as 'junior'
        - Correct, detailed, knowledgeable responses → rate as 'mid/senior'

        ✗ DOES NOT COUNT TOWARD PERCENTAGE (inadequate responses):
        - Completely wrong/incorrect answers showing fundamental misunderstanding → rate as inadequate, assign seniority based on overall candidate profile
        - Nonsensical or irrelevant responses → rate as inadequate, assign seniority based on overall candidate profile
        - Responses that demonstrate confusion about basic concepts → rate as inadequate, assign seniority based on overall candidate profile

        ✗ UNANSWERED (does not count toward percentage):
        - Complete silence, no verbal response at all
        - Question was asked but candidate never responded
        - Interviewer moved on without getting any response

        TASK INSTRUCTIONS:
        1. For each of the {len(transcript_questions.questions)} questions provided:
           - Find the corresponding candidate response in the transcript
           - CRITICAL: Use the expected answers provided in 'matched_questions_with_expected_answers' as your BENCHMARK
           - Each question may have expected answers for different seniority levels (junior_answer, mid_answer, senior_answer)
           - If expected answers are available, compare candidate's response against these specific benchmarks
           - If no expected answers are available for a question, use general evaluation criteria
           - Look for candidate responses in sections like "CANDIDATE:" or other clear response patterns
           - If ANY verbal response exists (even "I don't know"), classify as ANSWERED and rate the quality
           - If NO verbal response exists, classify as UNANSWERED
           - **Assess response quality by comparing to expected answers when available:**
             * 'senior': Response matches/exceeds the senior_answer quality, shows expert knowledge, strategic thinking
             * 'mid': Response shows competent, practical experience comparable to mid_answer
             * 'junior': Response shows foundational understanding comparable to junior_answer, learning mindset

        2. Calculate overall assessment:
           - percentage_of_match = (number of VALID responses / {len(transcript_questions.questions)}) * 100
           - IMPORTANT: Only count valid responses toward percentage (exclude wrong/incorrect answers)
           - overall_seniority using MANDATORY THRESHOLD-BASED CLASSIFICATION:
             * STEP 1: Count each adequate response by its detected_seniority level
             * STEP 2: Calculate percentages of each level
             * STEP 3: Apply this EXACT logic (NO EXCEPTIONS):
               - If 80%+ responses are senior → SET overall_seniority = "senior"
               - If 80%+ responses are mid → SET overall_seniority = "mid"
               - If 80%+ responses are junior → SET overall_seniority = "junior"
               - If no single level reaches 80%, use highest count (senior beats mid beats junior in ties)
             * EXAMPLE: 17 senior + 3 junior = 85% senior → MUST be "senior"
             * CRITICAL: Ignore traditional "averaging" - use ONLY this threshold method
           - Include analysis of feedback comments if provided

        3. Response quality guidelines (be appropriately generous, not overly conservative):
           - 'senior': ANY of the following qualifies as senior-level:
             * Demonstrates advanced technical knowledge beyond basic implementation
             * Shows strategic thinking, architectural considerations, or system design awareness
             * Mentions best practices, optimization, scalability, or maintainability concerns
             * Discusses trade-offs, alternatives, or decision-making rationale
             * Shows leadership experience, mentoring, or team coordination
             * Demonstrates ability to solve complex problems or handle edge cases
             * Shows deep understanding of underlying principles, not just surface knowledge
           - 'mid': Shows practical experience and competency:
             * Solid hands-on experience with tools/technologies
             * Can work independently on standard tasks
             * Understands common patterns and approaches
             * Shows problem-solving ability for typical scenarios
           - 'junior': Shows foundational knowledge and growth potential:
             * Basic understanding of concepts and terminology
             * Eagerness to learn and correct fundamental thinking
             * May need guidance but demonstrates logical reasoning

        4. Response adequacy assessment (separate from seniority level):
           - ADEQUATE: Any response that demonstrates understanding at junior, mid, or senior level
           - INADEQUATE: Responses that show fundamental misunderstanding, completely incorrect information, or no meaningful content
           - NO RESPONSE: Literally no verbal response provided by candidate

        CRITICAL COUNTING RULES:
        - Total questions to evaluate: {len(transcript_questions.questions)}
        - Each question MUST have a corresponding QuestionEvaluation in the per_question array
        - Count ADEQUATE responses toward percentage calculation (regardless of seniority level)
        - "I don't know" responses should be evaluated based on context: if honest acknowledgment of knowledge gaps, rate as junior; if showing no effort, rate as inadequate
        - DO NOT count INADEQUATE responses toward percentage (responses showing fundamental misunderstanding or no meaningful content)
        - Only mark as NO RESPONSE if there is genuinely NO verbal response from candidate
        - The per_question array must contain exactly {len(transcript_questions.questions)} evaluations

        HOW TO USE EXPECTED ANSWERS:
        - In 'matched_questions_with_expected_answers', each question has:
          * 'transcript_question': The question extracted from transcript
          * 'expected_answers': Object with junior_answer, mid_answer, senior_answer (if available)
        - When expected answers are available, use them as quality benchmarks:
          * Compare candidate response to junior_answer, mid_answer, senior_answer
          * Rate based on which level the response most closely matches
          * Consider depth, accuracy, and comprehensiveness relative to expected answers
        - When expected answers are NOT available, use general evaluation criteria

        EXAMPLES FOR CLARITY:
        Question: "Can you explain your experience with React?"
        Expected junior_answer: "I have basic understanding of React components and JSX"
        Expected mid_answer: "I have solid experience with React hooks, state management, and building SPAs"
        Expected senior_answer: "I architect React applications with performance optimization, custom hooks, and strategic state management decisions"

        - Response: "I don't know React but I'm eager to learn it" → ADEQUATE (honest, shows learning mindset), detected_seniority: 'junior'
        - Response: "I understand React components and JSX basics" → ADEQUATE (matches junior_answer level), detected_seniority: 'junior'
        - Response: "I have 2 years with React, built SPAs, understand hooks and state management" → ADEQUATE (matches mid_answer level), detected_seniority: 'mid'
        - Response: "I've architected React applications considering performance optimization, implemented custom hooks for reusability" → ADEQUATE (matches senior_answer level), detected_seniority: 'senior'
        - Response: "React is a database system for storing data" → INADEQUATE (fundamental misunderstanding), does not count toward percentage
        - No response in transcript → NO RESPONSE, does not count toward percentage

        SENIOR-LEVEL INDICATORS (any of these should qualify as senior):
        - Mentions architectural decisions, design patterns, or system design considerations
        - Discusses performance, optimization, scalability, or maintainability
        - Shows awareness of trade-offs and decision-making rationale
        - Demonstrates mentoring, leadership, or team coordination experience
        - Explains complex problem-solving or handling edge cases
        - Shows deep understanding of underlying principles beyond basic usage

        IMPORTANT:
        - Base evaluation ONLY on the {len(transcript_questions.questions)} questions provided in the questions_identified list
        - Provide output as valid JSON matching the schema
        - The per_question array length MUST equal {len(transcript_questions.questions)}
        - Include the total count of questions evaluated in your explanation
        - CRITICAL: If responses show clear senior-level indicators, classify as senior - don't default to mid due to conservative bias
        - Look for evidence of advanced competency, not perfection in every response
        """

    # Prepare evaluation context
    evaluation_context = {
        'transcript': transcript_tec,
        'questions_identified': [q.model_dump() for q in transcript_questions.questions],
        'feedback_comments': feedback_tec if feedback_tec else {},
        'matched_questions_with_expected_answers': matched_questions
    }

    schema_text = get_related_class_definitions(EvaluationResult)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        raise RuntimeError("LLM evaluation failed")

    # Validate evaluation results
    validation_issues = _validate_evaluation_result(result, len(transcript_questions.questions), "transcript-based")
    if validation_issues:
        logger.warning(f"TRANSCRIPT EVAL VALIDATION: Issues detected: {validation_issues}")
        # Log issues but don't fail - let the result through with warnings

    # Debug logging for evaluation results
    logger.info(f"TRANSCRIPT EVAL DEBUG: Evaluation completed")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Overall seniority: {result.overall_seniority}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Percentage of match: {result.percentage_of_match}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Number of per_question evaluations: {len(result.per_question)}")

    return result


# Evaluate the interview by comparing candidate answers with expected answers
def re_evaluate_interview(interview_id: str) -> Union[EvaluationResult, EvaluateInterviewNoQA]:
    """
    Evaluate the interview using transcript-based analysis for more accurate assessment.

    Args:
        interview_id (str): The ID of the interview to evaluate.

    Returns:
        Union[EvaluationResult, EvaluateInterviewNoQA]: The evaluation result.
        Returns EvaluationResult when transcript is available, EvaluateInterviewNoQA otherwise.
    """
    # First, try transcript-based evaluation for more accurate results
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.transcript_tec
                FROM interviews i
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            transcript_row = cur.fetchone()

        # If we have a transcript, use transcript-based evaluation
        if transcript_row and transcript_row[0] and transcript_row[0].strip():
            logger.info(f"Using transcript-based evaluation for interview {interview_id}")
            try:
                result = evaluate_interview_transcript_based(interview_id)

                # Persist the result
                with get_cursor() as cur:
                    cur.execute(
                        """
                        UPDATE interviews
                        SET interview_data = %s,
                            updated_at = NOW()
                        WHERE id = %s;
                        """,
                        (Json(result.model_dump()), interview_id)
                    )
                return result
            except Exception as eval_error:
                logger.error(f"Transcript-based evaluation failed for interview {interview_id}: {str(eval_error)}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                raise eval_error  # Re-raise to see the actual error

    except Exception as e:
        logger.error(f"Database error in transcript evaluation for interview {interview_id}: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Continue to fallback approaches

    # Fallback 1: Try predefined questions approach
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.anwers_data, i.feedback_tec, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    if not row:
        logger.info(f"No predefined questions found, using no-QA evaluation for interview {interview_id}")
        return evaluate_interview_with_no_qa(interview_id)

    expected_data, actual_answers, feedback_tec, transcript_tec = row

    # If expected questions are missing or empty, fallback to evaluation without QA
    if expected_data is None or not expected_data.get('questions'):
        logger.info(f"Empty predefined questions, using no-QA evaluation for interview {interview_id}")
        return evaluate_interview_with_no_qa(interview_id)

    # Fallback 2: Use predefined questions approach (legacy method)
    logger.info(f"Using predefined questions evaluation for interview {interview_id}")

    task_prompt = """
        You are evaluating a candidate's responses in a technical job interview. Your task is to analyze the transcript and assess each question the candidate answered.

        DATA SOURCES:
        - If all actual answers are "Invalid transcript," provide the explanation: "No enough information has been provided."
        - Use valid actual answers as the primary source.
        - Use the transcript_excerpt for additional context when available.

        RESPONSE CLASSIFICATION RULES (CRITICAL):
        For PERCENTAGE CALCULATION, distinguish between valid and invalid responses:

        ✓ COUNTS TOWARD PERCENTAGE (valid responses):
        - "I don't know", "I'm not sure", "No experience with that", "Never used it" → rate as 'junior'
        - Brief but relevant responses like "Yes", "No", "Maybe" → rate as 'junior'
        - Correct, detailed, knowledgeable responses → rate as 'mid/senior'

        ✗ DOES NOT COUNT TOWARD PERCENTAGE (inadequate responses):
        - Completely wrong/incorrect answers showing fundamental misunderstanding → rate as inadequate, assign seniority based on overall candidate profile
        - Nonsensical or irrelevant responses → rate as inadequate, assign seniority based on overall candidate profile
        - Responses that demonstrate confusion about basic concepts → rate as inadequate, assign seniority based on overall candidate profile

        ✗ UNANSWERED (does not count toward percentage):
        - Complete silence, no verbal response at all
        - Question was asked but candidate never responded
        - Interviewer moved on without getting any response

        TASK INSTRUCTIONS:
        1. For each question in the expected questions list:
           - COMPARE the candidate's actual response against the provided expected response
           - Use the expected response as the BENCHMARK for what constitutes a good answer
           - Check if there is ANY verbal response from the candidate
           - If ANY response exists, classify as ANSWERED and evaluate both adequacy and seniority level
           - If NO response exists, classify as UNANSWERED
        2. For each ANSWERED question:
           - CRITICAL: Compare candidate's response to the expected response to determine quality level
           - If candidate's response matches or exceeds the expected response quality → 'senior' or 'mid'
           - If candidate's response shows basic understanding but less detail than expected → 'mid' or 'junior'
           - If candidate shows foundational knowledge but minimal detail → 'junior'
           - First assess response adequacy: ADEQUATE (shows understanding) vs INADEQUATE (fundamental errors/no content)
           - For ADEQUATE responses, classify seniority level based on comparison to expected response:
             * 'senior': Response matches/exceeds expected response quality, shows advanced indicators like strategic thinking, best practices, architectural awareness, complex problem-solving, leadership experience, optimization concerns, or deep technical insights
             * 'mid': Response shows solid practical knowledge comparable to expected response, independent work capability, standard problem-solving, proven hands-on experience
             * 'junior': Response shows foundational understanding, learning mindset, basic concepts, growth potential, but less comprehensive than expected response
           - For INADEQUATE responses: Note as inadequate but still assign the most appropriate seniority level if any competency is shown
           - Provide clear explanation focusing on what the candidate demonstrated, not what they lacked
        3. For UNANSWERED questions:
           - Mark as unanswered and assign detected_seniority based on overall candidate profile, not automatically as 'junior'

        OVERALL ASSESSMENT - MANDATORY THRESHOLD-BASED CLASSIFICATION:
        - STEP 1: Count each adequate response by its detected_seniority level (senior_count, mid_count, junior_count)
        - STEP 2: Calculate percentages of each level from total adequate responses
        - STEP 3: Apply this EXACT threshold logic (MANDATORY - NO EXCEPTIONS):
          * If 80%+ responses are senior → SET overall_seniority = "senior"
          * If 80%+ responses are mid → SET overall_seniority = "mid"
          * If 80%+ responses are junior → SET overall_seniority = "junior"
          * If no single level reaches 80%, use highest count (senior > mid > junior for ties)
        - EXAMPLE: 17 senior + 3 junior = 85% senior → MUST set overall_seniority = "senior"
        - CRITICAL: Ignore traditional "averaging" or "predominant level" - use ONLY this threshold method
        - If all responses are inadequate, set overall_seniority to 'n/a' and explain: "Insufficient adequate responses to determine seniority level."
        - Candidate background is secondary - threshold calculation is PRIMARY

        PERCENTAGE CALCULATION:
        - percentage_of_match = (number of ADEQUATE responses / total questions) * 100
        - Count responses that show understanding and effort as ADEQUATE (regardless of seniority level)
        - Exclude responses that show fundamental misunderstanding or no meaningful content
        - Exclude questions with NO verbal response at all

        EXPLANATION REQUIREMENTS:
        - Focus on positive attributes and demonstrated competencies for each seniority level
        - For junior: Highlight foundational knowledge, learning attitude, and growth potential
        - For mid: Emphasize practical experience, problem-solving abilities, and independence
        - For senior: Showcase expertise, strategic thinking, and leadership capabilities
        - Avoid framing junior level as "lacking" or "poor" - instead emphasize appropriate level characteristics

        EXAMPLES FOR CLARITY:
        Question: "Describe your experience with microservices"
        - Response: "I don't know about microservices but I understand they're used for scalable applications" → ADEQUATE (honest + basic understanding), detected_seniority: 'junior'
        - Response: "I haven't worked with them yet, but I'm interested in learning" → ADEQUATE (honest acknowledgment + learning mindset), detected_seniority: 'junior'
        - Response: "Microservices are just big databases" → INADEQUATE (fundamental misunderstanding), does not count toward percentage
        - Response: "I've built microservices using Docker and handled API communication between services" → ADEQUATE, detected_seniority: 'mid'
        - Response: "I've designed microservice architectures considering service boundaries, data consistency patterns, and implemented circuit breakers for resilience" → ADEQUATE, detected_seniority: 'senior'
        - Response: "I've led the migration from monolith to microservices, considering team structure and deployment strategies" → ADEQUATE, detected_seniority: 'senior'
        - No response found in transcript/answers → NO RESPONSE, does not count toward percentage

        REMEMBER: Look for senior-level indicators like architectural thinking, best practices, optimization, leadership, or deep technical insights - don't require all of these, just clear evidence of advanced competency.

        ADDITIONAL REQUIREMENTS:
        - Incorporate percentage_of_match into the overall_explanation
        - Provide output as a valid JSON object only (no additional text)
        - Maintain consistent labeling: 'senior', 'mid', 'junior', or 'n/a' when no valid data

        Format explanations clearly for each question and the overall evaluation.
        """
    schema_text = get_related_class_definitions(EvaluationResult)

    # Prepare comprehensive context for LLM
    evaluation_context = {
        'expected': expected_data,
        'actual': actual_answers
    }

    # Add feedback comments if available
    if feedback_tec:
        evaluation_context['feedback_comments'] = feedback_tec

    # Add transcript excerpt if available (truncate if too long)
    if transcript_tec:
        transcript_excerpt = transcript_tec[:1000] + "..." if len(transcript_tec) > 1000 else transcript_tec
        evaluation_context['transcript_excerpt'] = transcript_excerpt

    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("LLM evaluation failed")

    # Validate evaluation results
    expected_questions_count = len(expected_data.get('questions', [])) if expected_data else 0
    validation_issues = _validate_evaluation_result(result, expected_questions_count, "predefined-questions")
    if validation_issues:
        logger.warning(f"PREDEFINED EVAL VALIDATION: Issues detected: {validation_issues}")
        # Log issues but don't fail - let the result through with warnings

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET interview_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


# Generate and persist interview questions
# This function generates interview questions based on the position ID and persists them in the database.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    This function generates interview questions based on the position ID and persists them in the database.

    Args:
        position_id (str): The ID of the position for which to generate questions.
        n_questions (int): The number of questions to generate.
        include (str): A comma-separated string of topics to include in the questions.
        current_user (str): The current user generating the questions.

    Returns:
        QA_model: The generated questions and related data.
    """
    # 1) Fetch position
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist for this position
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        result = cur.fetchone()  # fetchone can return None
        allow_regeneration = True if not result or result[1] is None else result[1]
        if not allow_regeneration:
            # If questions exist and regeneration is not allowed, raise an error
            raise HTTPException(
                status_code=400,
                detail=f"Interview questions already exist for position {position_id} and regeneration is not allowed"
            )

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    topics_text = get_topics(include)
    # print("topics_text", topics_text)
    task_prompt = f"""
        You are tasked with creating a structured interview questionnaire designed to evaluate **technical and methodological skills** while clearly differentiating levels of seniority among candidates for a specific role.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions based on the following topics: {topics_text}. For each question, ensure the output includes:**

        1. A sequential question_number ranging from 1 to {n_questions}.
        2. A single tag indicating the specific topic addressed, selected exclusively from: {topics_text}.
        3. Three distinct answers that reflect different seniority levels:
        - junior_answer
        - mid_answer
        - senior_answer

        **Guidelines for Answer Construction (Chain of Thought per level):**

        - senior_answer: Highlight advanced concepts, frameworks, and strategies. Emphasize decision-making, scalability, efficiency, and alignment with business value. Conclude with measurable outcomes or impact on organizational objectives.  
        - mid_answer: Describe practical execution, tools, and methodologies in detail. Show structured problem-solving and collaboration. Conclude with how these practices improve workflows or contribute to project/team success.  
        - junior_answer: Cover foundational concepts, learning in practice, and hands-on skills. Emphasize adaptability, eagerness to learn, and contribution to immediate team objectives.  

        **Formatting Rules:**
        - Deliver the output strictly in JSON format with valid syntax.  
        - Each topic from {topics_text} must appear in at least one question.  
        - Each question must have exactly one tag.  
        - Do not combine tags (e.g., "SOFT SKILLS METHODOLOGIES" is prohibited).  
        - Ensure clear differentiation between junior, mid, and senior answers — avoid repetition or generic filler.  
        - Avoid referencing seniority explicitly (e.g., "As a junior…" or "With X years of experience").  
        - Keep answers professional, substantive, and business-relevant.

        **Example (Agile Methodologies — Sprint Planning):**  
        - Junior: Basic understanding of Agile/Scrum, learning task organization, showing how participation supports team collaboration.  
        - Mid: Refining backlog, coordinating with stakeholders, ensuring adaptability and efficiency in delivery.  
        - Senior: Driving strategic alignment, leading planning sessions, ensuring measurable improvements in delivery and business outcomes.  
        """
    
    schema_text = get_related_class_definitions(QA_model)

    qa = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa:
        raise RuntimeError("LLM failed to generate questionnaire")

    # 3) Persist
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa.model_dump()), current_user, current_user)
        )
    return qa


# Create interviews for the given position and candidates
# This function creates interviews for the given position and candidates.
def create_interviews_for_position(position_id, analysis_data: list[InterviewCreate]) -> List[Interview]:
    """
    Create interviews for the given position and candidates.

    Args:
        position_id (str): The ID of the position for which to create interviews.
        analysis_data (list[InterviewCreate]): List of interview data for candidates.

    Returns:
        List[Interview]: List of created interview objects.
    """
    try:
        for data in analysis_data:
            candidate_id = data.candidate_id

            if not candidate_id:
                continue
            # Validate if candidate_id is a valid UUID
            if not isinstance(candidate_id, str) or len(candidate_id) != 36:
                continue
            # validate if candidate_id exists in candidates_smarthr table
            exist = get_candidate_by_id(candidate_id)
            if not exist:
                continue
            # Check if interview already exists for this candidate and position
            exist = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
            if exist:
                continue
            # Insert new interview
            with get_cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO interviews 
                    (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                    VALUES (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                    RETURNING id, position_id, candidate_id
                    """,
                    (
                        position_id,
                        candidate_id,
                        Json(data.analysis_data if data.analysis_data else {}),
                    ),
                )

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        print(f"Database error occurred while creating interview: {str(e)}")
        logger.error(f"Database error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interview.Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while creating interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while creating interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by position ID
# This function fetches all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    """
    Fetch all interviews for a given position ID.

    Args:
        position_id (str): The ID of the position for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id,),
            )
            rows = cur.fetchall()
        interviews = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Update interview feedback for HR
# This function updates the interview feedback for HR.
def update_interview_hr(interviewData: InterviewHr) -> Interview:
    """
    Update the interview feedback for HR.

    Args:
        interviewData (InterviewHr): The interview data to update.

    Returns:
        Interview: The updated interview object.
    """
    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found")

    if feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_hr),
        interviewData.recruiter_hr_id,
        interviewData.scheduled_hr_id,
        interviewData.interview_date_hr,
        interviewData.feedback_date_hr,
        interviewData.status_hr,
        interviewData.recommendation_hr,
        interviewData.transcript_hr,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )


# Update interview feedback for the technical team
# This function updates the interview feedback for the technical team.
def update_interview_tec(interviewData: InterviewTec) -> Interview:
    """
     Update the interview feedback for the technical team.

     Args:
         interviewData (InterviewTec): The interview data to update.

     Returns:
         Interview: The updated interview object.
    """
    # Debug logging
    logger.info(f"UPDATE_INTERVIEW_TEC: Received data for position_id={interviewData.position_id}, candidate_id={interviewData.candidate_id}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status received: '{interviewData.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {len(interviewData.transcript_tec) if interviewData.transcript_tec else 0}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript preview: {interviewData.transcript_tec[:200] if interviewData.transcript_tec else 'None'}...")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback keys: {list(interviewData.feedback_tec.keys()) if interviewData.feedback_tec else 'None'}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback content: {interviewData.feedback_tec}")

    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    logger.info(f"UPDATE_INTERVIEW_TEC: Current status in DB: '{feedback.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: StatusInterview.COMPLETED.value = '{StatusInterview.COMPLETED.value}'")

    if feedback.status_tec == StatusInterview.COMPLETED.value or feedback.status_tec == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET            
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_tec),
        interviewData.recruiter_tec_id,
        interviewData.scheduled_tec_id,
        interviewData.interview_date_tec,
        interviewData.feedback_date_tec,
        interviewData.status_tec,
        interviewData.recommendation_tec,
        interviewData.transcript_tec,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    response = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )

    # fill anwers_data from transcript_tec after is completed
    logger.info(f"UPDATE_INTERVIEW_TEC: Checking if evaluation should run. Status: '{response.status_tec}', Expected: '{StatusInterview.COMPLETED.value}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status comparison result: {response.status_tec == StatusInterview.COMPLETED.value}")

    if response.status_tec == StatusInterview.COMPLETED.value:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is COMPLETED, starting evaluation process for interview {response.id}")

        # Check if we have transcript data or feedback data to evaluate
        has_transcript = response.transcript_tec and response.transcript_tec.strip()
        transcript_length = len(response.transcript_tec) if response.transcript_tec else 0
        has_feedback_data = response.feedback_tec and isinstance(response.feedback_tec, dict)

        logger.info(f"UPDATE_INTERVIEW_TEC: Has transcript: {has_transcript}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {transcript_length}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Has feedback data: {has_feedback_data}")

        # Check if transcript is too short to be meaningful
        if has_transcript and transcript_length < 100:
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript is very short ({transcript_length} chars), might be truncated")
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript content: '{response.transcript_tec}'")

        if has_transcript or has_feedback_data:
            try:
                # Extract answers from transcript and persist to database
                extraction_result = run_and_persist_interview(response.id, ProcessType.EXTRACT)
                logger.info(f"UPDATE_INTERVIEW_TEC: Extraction result: {extraction_result}")

                # Only proceed with evaluation if extraction was successful
                if extraction_result:
                    # evaluate: anwers_data against questionaire, and persist interview_data
                    logger.info(f"Starting re-evaluation for interview {response.id}")
                    re_evaluate_interview(response.id)
                    logger.info(f"Re-evaluation completed for interview {response.id}")
                else:
                    logger.warning(f"UPDATE_INTERVIEW_TEC: Extraction failed for interview {response.id}")
                    # Try direct evaluation if we have feedback data
                    if has_feedback_data:
                        logger.info(f"UPDATE_INTERVIEW_TEC: Attempting direct evaluation using feedback data")
                        re_evaluate_interview(response.id)
            except Exception as e:
                logger.error(f"Error during interview processing for {response.id}: {str(e)}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                # Don't re-raise the exception to avoid breaking the update operation
                # The interview update should still succeed even if evaluation fails
        else:
            logger.warning(f"UPDATE_INTERVIEW_TEC: No transcript or feedback data to evaluate for interview {response.id}")
            # Create a basic evaluation indicating no data was provided
            try:
                from models.interview import EvaluateInterviewNoQA, Seniority
                basic_evaluation = EvaluateInterviewNoQA(
                    overall_seniority=Seniority.NA,
                    percentage_of_match=0.0,
                    explanation="No technical interview data was provided. The transcript field appears to be empty or incomplete. Please ensure the full interview transcript with questions and candidate responses is included when submitting technical feedback."
                )

                # Persist this basic evaluation
                with get_cursor() as cur:
                    cur.execute(
                        """
                        UPDATE interviews
                        SET interview_data = %s,
                            updated_at = NOW()
                        WHERE id = %s;
                        """,
                        (Json(basic_evaluation.model_dump()), response.id)
                    )
                logger.info(f"UPDATE_INTERVIEW_TEC: Created basic evaluation for incomplete data in interview {response.id}")
            except Exception as eval_error:
                logger.error(f"UPDATE_INTERVIEW_TEC: Failed to create basic evaluation: {str(eval_error)}")
    else:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is not COMPLETED, skipping evaluation. Status: '{response.status_tec}'")

    return response


# Get a single interview by position ID and candidate ID
# This function fetches a single interview for the given position and candidate IDs.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    """
     Fetch a single interview for the given position and candidate IDs.

     Args:
         position_id (str): The ID of the position.
         candidate_id (str): The ID of the candidate.

     Returns:
         Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id, candidate_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get questions by position ID
# This function fetches the questions for the given position ID.
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    """
    Fetch the questions for the given position ID.

    Args:
        position_id (str): The ID of the position.

    Returns:
        SingleQuestions: The questions object for the specified position ID.
    """
    try:
        with get_cursor() as cur:
            sqlQuery = """
                SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
                FROM interview_questions WHERE position_id::text=%s
            """
            params = (position_id,)
            cur.execute(sqlQuery, params)
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Questions not found")
            return SingleQuestions(
                id=str(row[0]),
                position_id=str(row[1]),
                data=row[2],
                created_at=row[3],
                updated_at=row[4],
                allow_regeneration=row[5],
                created_by=row[6],
                updated_by=row[7]
            )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Delete interview by position ID and candidate ID
# This function deletes an interview for the given position and candidate IDs.
def delete_interview(position_id: str, candidate_id: str) -> bool:
    """
    Delete an interview for the given position and candidate IDs.

    Args:
        position_id (str): The ID of the position.
        candidate_id (str): The ID of the candidate.

    Returns:
        bool: True if the interview was deleted successfully, False otherwise.
    """
    try:
        # Fetch the interview to check its status
        feedback = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not feedback:
            raise HTTPException(status_code=404, detail="Interview not found")
        # You may add status checks here if needed

        with get_cursor() as cur:
            cur.execute(
                """
                DELETE FROM interviews WHERE id = %s
                """,
                (feedback.id,),
            )
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while deleting interview: {str(e)}")
        logger.error(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while deleting interview: {str(e)}")
        logger.error(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by candidate ID
# This function fetches all interviews for the given candidate ID.
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    """
    Fetch all interviews for the given candidate ID.

    Args:
        candidate_id (str): The ID of the candidate for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (candidate_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch interview by interview ID
# This function fetches an interview by its ID.
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    """
    Fetch an interview by its ID.

    Args:
        interview_id (str): The ID of the interview to fetch.

    Returns:
        Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (interview_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Re-evaluate an interview by its ID
# This function re-evaluates an interview by its ID.
# It fetches the interview data, extract answers data if needed, evaluates it, and then fetches the updated interview data.
def re_evalute_interview(interview_id: str) -> Interview:
    """
    Re-evaluate an interview by its ID.

    Args:       
        interview_id (str): The ID of the interview to re-evaluate.

    Returns:
        Interview: The updated interview object.
    """
    # 1. Fetch the interview data
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical Transcript not found")

    if not interview.anwers_data:
        # 2. Run and persist the interview
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # 3. Evaluate the interview
    re_evaluate_interview(interview.id)

    # 4. Fetch the updated interview data
    interview = fetch_interview_by_interview_id(interview.id)
    return interview


# Change Questions status
def update_question_regeneration_status(position_id: str, question_id: str, allow_regeneration: bool) -> bool:
    """
    Change the status of a question in the interview_questions table.
    :param position_id: The ID of the position to which the question belongs.
    :param question_id: The ID of the question to update.
    :param allow_regeneration: Boolean indicating whether regeneration is allowed.
    :return: True if the update was successful, False otherwise.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interview_questions
            SET allow_regeneration = %s,
                updated_at = NOW()
            WHERE id = %s AND position_id = %s;
            """,
            (allow_regeneration, question_id, position_id)
        )
        if cur.rowcount == 0:
            raise HTTPException(status_code=404, detail="Question not found")
        return True
    return False
